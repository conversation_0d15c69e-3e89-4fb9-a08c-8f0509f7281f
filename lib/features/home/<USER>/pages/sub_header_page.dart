import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;
import 'package:storetrack_app/features/home/<USER>/widgets/progress_item_card.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';

@RoutePage()
class SubHeaderPage extends StatelessWidget {
  final String title;
  final List<entities.QuestionPart> questionParts;

  const SubHeaderPage({
    super.key,
    required this.title,
    required this.questionParts,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      backgroundColor: AppColors.lightGrey2,
      appBar: CustomAppBar(
        title: title,
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Gap(8),
            questionParts.isEmpty
                ? Center(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Text(
                        'No items available for this section',
                        style: textTheme.bodyLarge,
                      ),
                    ),
                  )
                : ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    padding: const EdgeInsets.symmetric(
                        horizontal: 8.0, vertical: 8.0),
                    itemCount: questionParts.length,
                    itemBuilder: (context, index) {
                      final part = questionParts[index];
                      return ProgressItemCard(
                        title: part.questionpartDescription ?? 'Unnamed Part',
                        progress: 0.0, // Default progress
                        progressText: '', // Default progress text or hide
                        width: 1.0, // Full width for list view
                        // onTap: () {
                        //   // Handle tap if necessary for further navigation
                        // },
                      );
                    },
                  ),
            const Gap(24),
          ],
        ),
      ),
    );
  }
}
