import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';

@RoutePage()
class FQPDPage extends StatelessWidget {
  const FQPDPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(title: 'FQPD Page'),
      body: const Center(
        child: Text('FQPD Page Content'),
      ),
    );
  }
}
