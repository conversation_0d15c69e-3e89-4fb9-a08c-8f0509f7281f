import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';

@RoutePage()
class QPMDPage extends StatelessWidget {
  const QPMDPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(title: 'QPMD Page'),
      body: const Center(
        child: Text('QPMD Page Content'),
      ),
    );
  }
}
