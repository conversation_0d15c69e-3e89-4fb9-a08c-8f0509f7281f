import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;
import 'package:storetrack_app/features/home/<USER>/pages/question_page.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/progress_item_card.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';

@RoutePage()
class FormPage extends StatefulWidget {
  final entities.TaskDetail task;

  const FormPage({
    super.key,
    required this.task,
  });

  @override
  State<FormPage> createState() => _FormPageState();
}

class _FormPageState extends State<FormPage> {
  // Get forms from the task
  List<entities.Form>? get formItems => widget.task.forms;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      backgroundColor: AppColors.lightGrey2,
      appBar: const CustomAppBar(
        title: 'Forms',
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Gap(8),
            formItems == null || formItems!.isEmpty
                ? Center(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Text(
                        'No forms available for this task',
                        style: textTheme.bodyLarge,
                      ),
                    ),
                  )
                : ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    padding: const EdgeInsets.symmetric(
                        horizontal: 8.0, vertical: 8.0),
                    itemCount: formItems!.length,
                    itemBuilder: (context, index) {
                      final form = formItems![index];

                      // Calculate progress based on question answers if available
                      double progress = 0.0;
                      String progressText = '0 of 0';

                      if (form.questions != null &&
                          form.questions!.isNotEmpty) {
                        int totalQuestions = form.questions!.length;
                        int answeredQuestions = 0;

                        if (form.questionAnswers != null) {
                          // Count answered questions
                          answeredQuestions = form.questionAnswers!.length;
                        }

                        progress = totalQuestions > 0
                            ? answeredQuestions / totalQuestions
                            : 0.0;
                        progressText = '$answeredQuestions of $totalQuestions';
                      }

                      return GestureDetector(
                        onTap: () {
                          // Navigate to QuestionPage when form is tapped
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (context) => QuestionPage(form: form),
                            ),
                          );
                        },
                        child: ProgressItemCard(
                          title: form.formName ?? 'Unnamed Form',
                          progress: progress,
                          progressText: progressText,
                          width: 1.0, // Full width for list view
                        ),
                      );
                    },
                  ),
            const Gap(24),
          ],
        ),
      ),
    );
  }
}
